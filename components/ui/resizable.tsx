"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface ResizableSplitPaneProps {
  leftPanel: React.ReactNode
  rightPanel: React.ReactNode
  defaultLeftWidth?: number // in pixels
  minLeftWidth?: number // in pixels
  maxLeftWidth?: number // in pixels
  className?: string
}

// Custom hook for responsive breakpoint detection
function useResponsiveBreakpoint() {
  const [breakpoint, setBreakpoint] = React.useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) {
        setBreakpoint('mobile')
      } else if (width < 1024) {
        setBreakpoint('tablet')
      } else {
        setBreakpoint('desktop')
      }
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return breakpoint
}



export function ResizableSplitPane({
  leftPanel,
  rightPanel,
  defaultLeftWidth = 480, // Default to 480px to accommodate monitor cards (320px + padding)
  minLeftWidth = 360, // Minimum 360px
  maxLeftWidth = 800, // Maximum 800px
  className
}: ResizableSplitPaneProps) {
  const [leftWidth, setLeftWidth] = React.useState(defaultLeftWidth)
  const [isDragging, setIsDragging] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)
  const breakpoint = useResponsiveBreakpoint()

  // Responsive width constraints based on breakpoint
  const getResponsiveConstraints = React.useCallback(() => {
    switch (breakpoint) {
      case 'mobile':
        return { min: 0, max: 0 } // Full width on mobile
      case 'tablet':
        return { min: 280, max: 400 }
      default:
        return { min: minLeftWidth, max: maxLeftWidth }
    }
  }, [breakpoint, minLeftWidth, maxLeftWidth])

  // Update width when breakpoint changes
  React.useEffect(() => {
    const constraints = getResponsiveConstraints()
    if (breakpoint === 'mobile') {
      // On mobile, we don't use split layout
      return
    }

    // Adjust current width to fit new constraints
    setLeftWidth(prev => Math.max(constraints.min, Math.min(constraints.max, prev)))
  }, [breakpoint, getResponsiveConstraints])

  const handleMouseDown = React.useCallback((e: React.MouseEvent) => {
    if (breakpoint === 'mobile') return // Disable dragging on mobile
    setIsDragging(true)
    e.preventDefault()
  }, [breakpoint])

  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !containerRef.current || breakpoint === 'mobile') return

      const containerRect = containerRef.current.getBoundingClientRect()
      const newLeftWidth = e.clientX - containerRect.left

      // Apply responsive constraints
      const constraints = getResponsiveConstraints()
      const constrainedWidth = Math.max(
        constraints.min,
        Math.min(constraints.max, newLeftWidth)
      )

      setLeftWidth(constrainedWidth)
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging && breakpoint !== 'mobile') {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp, { once: true })
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, breakpoint, getResponsiveConstraints])

  // Mobile layout: stack vertically
  if (breakpoint === 'mobile') {
    return (
      <div
        ref={containerRef}
        className={cn("responsive-split-container", className)}
      >
        {/* Left Panel - Top on mobile */}
        <div className="left-panel flex flex-col overflow-hidden">
          {leftPanel}
        </div>

        {/* Right Panel - Bottom on mobile */}
        <div className="right-panel flex flex-col overflow-hidden">
          {rightPanel}
        </div>
      </div>
    )
  }

  // Desktop/Tablet layout: side by side with resizer
  return (
    <div
      ref={containerRef}
      className={cn("responsive-split-container flex h-full w-full overflow-hidden", className)}
    >
      {/* Left Panel */}
      <div
        className="left-panel flex flex-col border-r border-border h-full overflow-hidden"
        style={{ width: `${leftWidth}px` }}
      >
        {leftPanel}
      </div>

      {/* Resize Handle */}
      <div
        className={cn(
          "resize-handle w-1 bg-border hover:bg-primary/20 cursor-col-resize transition-colors relative group",
          isDragging && "bg-primary/30",
          breakpoint === 'mobile' && "hidden"
        )}
        onMouseDown={handleMouseDown}
      >
        {/* Visual handle indicator */}
        <div className="absolute inset-y-0 left-0 w-1 bg-transparent group-hover:bg-primary/10 transition-colors" />
      </div>

      {/* Right Panel */}
      <div className="right-panel flex-1 flex flex-col h-full overflow-hidden preview-panel-responsive">
        {rightPanel}
      </div>
    </div>
  )
}
